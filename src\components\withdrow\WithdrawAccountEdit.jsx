"use client";

import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import React, { useEffect, useState } from "react";

function WithdrawAccountEdit() {
  // use network
  const networkService = new NetworkService();
  const [editAccountData, setEditAccountData] = useState([]);

  console.log("editAccountData", editAccountData);

  // fetch account data
  const fetchEditAccountsData = async () => {
    try {
      const res = await networkService.get(ApiPath.withdrawAccounts);
      if (res.status === "completed") {
        setEditAccountData(res.data.data.accounts);
      }
    } finally {
    }
  };

  useEffect(() => {
    fetchEditAccountsData();
  }, []);

  return (
    <>
      <div className="space-y-6">
        {/* Main Form */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Link
                href="/withdraw/withdraw-account"
                className="inline-flex items-center p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Edit Withdrawal Account
              </h2>
            </div>
          </div>
          <div className="grid grid-cols-12">
            <div className="col-span-6">
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    Edit Withdrawal Account
                  </h2>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Edit your withdrawal account details.
                  </p>
                </div>
                <div className="p-6">
                  <form className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Select Wallet
                      </label>
                      {/* <select
                      value={selectedWallet}
                      onChange={handleWalletChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select a wallet</option>
                      {walletData.map((wallet) => (
                        <option key={wallet.id} value={wallet.id}>
                          {wallet.name} {wallet.code}
                        </option>
                      ))}
                    </select> */}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Withdraw Method
                      </label>
                      {/* <select
                      value={selectedMethod}
                      onChange={(e) => setSelectedMethod(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select a method</option>
                      {withdrawMethod.map((method) => (
                        <option key={method.id} value={method.id}>
                          {method.name}
                        </option>
                      ))}
                    </select> */}
                    </div>
                  
                    <button
                      type="submit"
                      className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Add Account
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default WithdrawAccountEdit;
