"use client";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import Cookies from "js-cookie";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";

const MyAllWallets = ({ onWalletCreated }) => {
  // use network
  const networkService = new NetworkService();

  // state
  const [walletsCardsData, setWalletsCardsData] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch all wallets
  const fetchAllWallets = async () => {
    try {
      const res = await networkService.get(ApiPath.allWallets);
      setWalletsCardsData(res.data.data);
    } finally {
    }
  };

  // Fetch all currencies
  const fetchCurrencies = async () => {
    try {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-currencies`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setCurrencies(data.data);
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to fetch currencies"
      );
    }
  };

  // Filter only currencies user doesn't already have
  const getAvailableCurrencies = () => {
    const existingCodes = walletsCardsData?.wallets.map((w) => w.code);
    return currencies.filter((c) => !existingCodes.includes(c.code));
  };

  // card style
  const cardStyles = [
    {
      gradient: "from-indigo-600 via-purple-600 to-purple-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-blue-600 via-indigo-600 to-purple-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-cyan-500 via-blue-500 to-indigo-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-violet-600 via-purple-600 to-indigo-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-emerald-500 via-teal-600 to-cyan-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
  ];

  // Create a wallet
  const createWallet = async () => {
    try {
      setLoading(true);
      const res = await networkService.post(ApiPath.createWallet, {
        currency_id: selectedCurrency,
      });
      if (res.status === "completed") {
        toast.success(res.data.message);
        fetchAllWallets();
        closeModal();
      }
    } finally {
      setLoading(false);
    }
  };

  const openModal = () => {
    if (getAvailableCurrencies().length === 0) {
      toast.info("You already have wallets for all available currencies");
      return;
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCurrency("");
  };

  useEffect(() => {
    fetchCurrencies();
    fetchAllWallets();
  }, []);

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Wallets
          </h1>
          <button
            onClick={openModal}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Add New Wallet
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Wallet Overview
            </h2>
          </div>
          <div className="p-6">
            {/* Wallets Container */}
            {walletsCardsData?.wallets?.length > 0 ? (
              <div className="flex gap-6 w-full overflow-x-auto pb-4">
                {walletsCardsData?.wallets?.map((wallet, i) => {
                  const style = cardStyles[i % cardStyles.length];

                  return (
                    <div
                      key={wallet.id}
                      className={`bg-gradient-to-br ${style.gradient} 
                rounded-3xl min-w-[300px] p-6 text-white shadow-xl relative overflow-hidden
                backdrop-blur-xl`}
                    >
                      {/* Background Pattern */}
                      <div className="absolute inset-0">
                        <div className="absolute top-0 right-0 w-40 h-40 bg-white/5 rounded-full translate-x-16 -translate-y-16"></div>
                        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full -translate-x-10 translate-y-10"></div>
                      </div>

                      {/* Content */}
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center gap-4">
                            <div
                              className={`w-12 h-12 ${style.iconBg} rounded-2xl flex items-center justify-center backdrop-blur-sm shadow-lg`}
                            >
                              {wallet.icon && wallet.icon !== "null" ? (
                                <img
                                  src={wallet.icon}
                                  alt={wallet.name}
                                  className="w-7 h-7"
                                />
                              ) : (
                                <span className="font-bold text-lg">
                                  {wallet.symbol}
                                </span>
                              )}
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg leading-tight">
                                {wallet.name}
                              </h3>
                              <p className="text-white/70 text-sm font-medium">
                                {wallet.code}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Balance Section */}
                        <div className="mb-6">
                          <p className="text-white/70 text-sm font-medium mb-2">
                            Current Balance
                          </p>
                          <h2 className="text-2xl font-bold leading-tight mb-1">
                            {wallet.formatted_balance}
                          </h2>
                          <p className="text-white/80 text-lg font-medium">
                            {wallet.code}
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-3">
                          <button
                            className={`${style.accent} px-0 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                          >
                            <span className="flex items-center justify-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M12 4v16m8-8H4"
                                />
                              </svg>
                              Top Up
                            </span>
                          </button>
                          <button
                            className={`${style.accent} px-3 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                          >
                            <span className="flex items-center justify-center gap-2">
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M20 12H4"
                                />
                              </svg>
                              Withdraw
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400 text-center">
                  No wallets found. Create your first wallet to get started.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default MyAllWallets;
