"use client";

import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";

function APIAccessLayout() {
  // use network
  const networkService = new NetworkService();

  const [publicAPIKey, setPublicAPIKey] = useState("");
  const [secretAPIKey, setSecretAPIKey] = useState("");
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);

  // Fetch API keys from server
  const fetchApiKeys = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(ApiPath.apiKeys);
      if (res.status === "completed") {
        const data = res.data.data;
        setPublicAPIKey(data.public_key || "");
        setSecretAPIKey(data.secret_key || "");
      } else {
        setPublicAPIKey("");
        setSecretAPIKey("");
      }
    } finally {
      setLoading(false);
    }
  };

  // Copy key to clipboard
  const copyToClipboard = (key) => {
    navigator.clipboard.writeText(key);
    toast.success("Copied to clipboard!");
  };

  // Generate new API keys
  const generateNewKey = async () => {
    setGenerating(true);
    try {
      const res = await networkService.post(ApiPath.generateApiKey);
      if (res.status === "completed") {
        const data = res.data.data;
        setPublicAPIKey(data.public_key);
        setSecretAPIKey(data.secret_key);
        toast.success("New API keys generated successfully!");
      } else {
        toast.error("Failed to generate new API keys.");
      }
    } finally {
      setGenerating(false);
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          API Access Keys
        </h1>
        <button
          onClick={generateNewKey}
          disabled={generating}
          className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            generating
              ? "bg-gray-400 text-gray-200 cursor-not-allowed"
              : "bg-blue-600 text-white hover:bg-blue-700"
          }`}
        >
          {generating ? "Generating..." : "Generate New Key"}
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            API Keys Management
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage your API keys for secure access. Keep them private.
          </p>
        </div>
        <div className="p-6 space-y-6">
          {/* Security Notice */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 flex items-start gap-3">
            <svg
              className="h-5 w-5 text-yellow-400 flex-shrink-0 mt-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Security Notice
              </h3>
              <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                Keep your API keys secure. Never share them publicly. Regenerate
                keys if compromised.
              </p>
            </div>
          </div>

          {loading ? (
            <p className="text-gray-500 dark:text-gray-400 text-center">
              Loading API keys...
            </p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Public Key */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Public Key
                </label>
                <input
                  type="text"
                  value={publicAPIKey}
                  readOnly
                  className="mt-1 block w-full pr-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <button
                  type="button"
                  onClick={() => copyToClipboard(publicAPIKey)}
                  className="absolute right-1 top-[44px] transform -translate-y-1/2 px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Copy
                </button>
              </div>

              {/* Secret Key */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Secret Key
                </label>
                <input
                  type="text"
                  value={secretAPIKey}
                  readOnly
                  className="mt-1 block w-full pr-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <button
                  type="button"
                  onClick={() => copyToClipboard(secretAPIKey)}
                  className="absolute right-1 top-[44px] transform -translate-y-1/2 px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Copy
                </button>
              </div>
            </div>
          )}

          {!publicAPIKey && !secretAPIKey && !loading && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                No API keys found. Generate your first API key to get started.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default APIAccessLayout;
